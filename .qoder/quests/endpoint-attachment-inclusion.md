# PQRSF Consultation Endpoint - Attachment Inclusion Analysis

## Overview

This design document analyzes the current PQRSF consultation endpoint and evaluates the complexity of adding attachment information to the API response. The endpoint currently provides basic information about PQRSF incidents but excludes attachment details that are associated with each incident through a many-to-many relationship.

**Current Endpoint**: `GET /pqrsf/consultas`
**Query Parameter**: `radicado` (string)
**Current Response**: Basic incident information without attachments

## Current Architecture Analysis

### Endpoint Implementation

The `/pqrsf/consultas` endpoint is implemented in the `GestionPQRSF` class and follows this flow:

1. **Query Execution**: Retrieves `IncidenciaGeneral` entities by `radicado` using `IncidenciaJpaRepository.finByRadicado()`
2. **Response Construction**: Builds JSON response with core incident data
3. **Response Enhancement**: Conditionally includes response document information if available

### Current Response Structure

| Field | Type | Description |
|-------|------|-------------|
| radicado | String | Incident filing number |
| asunto | String | Subject of the incident |
| fechaRadicado | Long | Creation timestamp |
| fechaRespuesta | Long | Response timestamp (if answered) |
| respuesta | String | PDF download URL (if answered) |
| radicadoEnvio | String | Response filing number (if answered) |

### Data Model Relationships

```mermaid
erDiagram
    IncidenciaGeneral ||--o{ Attachment : "many-to-many"
    IncidenciaGeneral ||--|| TipoDocumental : "belongs to"
    IncidenciaGeneral ||--|| Entidad : "belongs to"
    IncidenciaGeneral ||--o| Documento : "has response"
    
    Attachment ||--o{ TraceabilityAttachment : "has traceability"
    
    IncidenciaGeneral {
        Integer id
        String radicado
        String asunto
        Date fechaCreacion
        Date fechaRespuesta
        List attachments
    }
    
    Attachment {
        Integer id
        String fileName
        String hash
        String format
        String descripcion
    }
```

## Attachment System Architecture

### Attachment Entity Structure

| Field | Type | Description |
|-------|------|-------------|
| id | Integer | Primary key |
| fileName | String | Original file name |
| hash | String | File integrity verification |
| format | String | File extension/type |
| descripcion | String | File description |

### Attachment Storage Strategy

The system implements local file storage:

- **Local Storage**: Files stored directly in the application file system
- **File Management**: Direct file system access through existing infrastructure

### Existing Download Infrastructure

The system provides download capabilities for local files:

- **Local Files**: Direct file system access through `DownloadResource`
- **Traceability**: All download actions are tracked via `TraceabilityAttachment`

## Enhancement Design: Attachment Inclusion

### Proposed Response Enhancement

#### Enhanced Response Structure

``mermaid
graph TD
    A[API Response] --> B[Core Incident Data]
    A --> C[Attachment Array]
    
    B --> D[radicado]
    B --> E[asunto]
    B --> F[fechaRadicado]
    B --> G[Response Data if available]
    
    C --> H[Attachment Object 1]
    C --> I[Attachment Object N]
    
    H --> J[id]
    H --> K[fileName]
    H --> L[descripcion]
    H --> M[format]
    H --> N[downloadUrl]
```

#### Attachment Object Schema

| Field | Type | Description |
|-------|------|-------------|
| id | Integer | Attachment unique identifier |
| fileName | String | Original file name |
| descripcion | String | File description |
| format | String | File type/extension |
| downloadUrl | String | Direct download URL |

### Implementation Strategy

#### Response Construction Logic

1. **Attachment Retrieval**: Fetch attachments through existing `IncidenciaGeneral.getAttachments()` relationship
2. **URL Generation**: Create download URLs for local files using `/webresources/Download/attachment?id={attachmentId}` pattern
3. **Response Assembly**: Include attachment array in existing JSON response structure

#### Download URL Generation Pattern

``mermaid
flowchart TD
    A[Generate Download URL] --> B[Generate Local Download URL]
    B --> C[DownloadResource Endpoint]
    B --> D[File System Access]
    C --> E[Local File Stream]
```

### Service Layer Extensions

#### Enhanced Endpoint Implementation

The enhancement requires minimal service layer modifications:

1. **Repository Layer**: No changes required - existing relationship mapping is sufficient
2. **Service Layer**: Extend response building logic to include attachment processing
3. **URL Construction**: Implement attachment URL generation utility

#### URL Construction Strategy

| Storage Type | URL Pattern | Implementation |
|--------------|-------------|----------------|
| Local | `/webresources/Download/attachment?id={id}` | Extend existing DownloadResource |

## Complexity Assessment

### Implementation Difficulty: **LOW**

#### Factors Supporting Low Complexity

1. **Existing Relationships**: JPA many-to-many mapping already established
2. **Download Infrastructure**: Complete download system already implemented
3. **URL Generation**: Patterns established for document downloads
4. **Data Access**: No additional database queries required

#### Factors Increasing Complexity

1. **URL Generation Logic**: Implementing attachment-specific download endpoints
2. **Error Handling**: Managing scenarios where local files are unavailable using existing patterns

### Development Effort Estimation

| Task | Estimated Effort | Complexity |
|------|------------------|------------|
| Response Model Enhancement | 2-3 hours | Low |
| URL Generation Implementation | 3-4 hours | Low-Medium |
| Endpoint Modification | 2-3 hours | Low |
| Manual Testing | 2-3 hours | Low |
| **Total Estimated Effort** | **9-13 hours** | **Low** |

### Technical Considerations

#### Performance Optimization

1. **Lazy Loading**: Attachments are already configured with `FetchType.LAZY`
2. **Query Optimization**: Single query retrieves incident with attachments

#### Security Considerations

1. **Access Control**: Follow existing patterns from DownloadResource endpoint
2. **File Access**: Use existing file system security mechanisms

#### Error Handling Strategy

1. **Missing Files**: Return empty array when attachments are unavailable
2. **File Access Issues**: Use existing error handling patterns from GestionPQRSF class
3. **File System Issues**: Follow current exception handling approach in the endpoint

### Recommended Implementation Approach

#### Single Implementation Phase
- Modify existing `getJson` method in `GestionPQRSF` to include attachment information
- Generate download URLs using existing DownloadResource pattern
- Use existing error handling patterns (try-catch with 409 status)
- Follow current JSON response building approach with `Json.createObjectBuilder()`

## Testing Strategy

### Manual Testing Approach

Following the existing testing patterns in the application:

| Test Scenario | Validation Method |
|---------------|------------------|
| Endpoint Response | Manual API testing with radicado parameter |
| Attachment Array | Verify JSON structure includes attachment information |
| Download URLs | Test generated URLs in browser |
| Error Handling | Test with non-existent radicado values |

### Testing Process

1. **Unit Testing**: Extend existing test classes following current patterns
2. **Manual Validation**: Browser-based testing using existing endpoints
3. **Error Scenarios**: Test missing attachments and invalid radicado values
