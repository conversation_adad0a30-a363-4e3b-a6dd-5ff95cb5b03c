/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.pqrsd;

import com.base16.gedsys.domain.model.entityListeners.TrazabilidadPQRSFListener;
import com.base16.gedsys.domain.model.user.Usuario;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "trazabilidadPQRSF", catalog = "", schema = "")
@EntityListeners(TrazabilidadPQRSFListener.class)
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "TrazabilidadPQRSF.findAll", query = "SELECT t FROM TrazabilidadPQRSF t"),
    @NamedQuery(name = "TrazabilidadPQRSF.findById", query = "SELECT t FROM TrazabilidadPQRSF t WHERE t.id = :id"),
    @NamedQuery(name = "TrazabilidadPQRSF.findByUsuario", query = "SELECT t FROM TrazabilidadPQRSF t WHERE t.usuario = :usuario"),
    @NamedQuery(name = "TrazabilidadPQRSF.findByFechaRegistro", query = "SELECT t FROM TrazabilidadPQRSF t WHERE t.fechaRegistro = :fechaRegistro")})
public class TrazabilidadPQRSF implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    @Basic(optional = false)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Integer id;
    @Lob
    @Size(max = 2147483647)
    @Column(name = "accion")
    private String accion;
    @Column(name = "fechaCreacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaRegistro;
    @JoinColumn(name = "especifica", referencedColumnName = "Id")
    @ManyToOne
    private IncidenciaEspecifica pqrsfEspecifica;
    @JoinColumn(name = "general", referencedColumnName = "Id")
    @ManyToOne
    private IncidenciaGeneral pqrsfGeneral;
    @JoinColumn(name = "Usuario", referencedColumnName = "Id")
    @ManyToOne
    private Usuario usuario;

    public TrazabilidadPQRSF() {
    }

    public TrazabilidadPQRSF(String accion, IncidenciaGeneral incidenciaGeneral, Usuario usuario){
        setAccion(accion);
        setPqrsfGeneral(incidenciaGeneral);
        setUsuario(usuario);
    }

    public TrazabilidadPQRSF(String accion, IncidenciaEspecifica incidenciaEspecifica, Usuario usuario){
        setAccion(accion);
        setPqrsfEspecifica(incidenciaEspecifica);
        setUsuario(usuario);
    }

    public TrazabilidadPQRSF(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getAccion() {
        return accion;
    }

    public void setAccion(String accion) {
        this.accion = accion;
    }

    public Date getFechaRegistro() {
        return fechaRegistro;
    }

    public void setFechaRegistro(Date fechaRegistro) {
        this.fechaRegistro = fechaRegistro;
    }

    public IncidenciaEspecifica getPqrsfEspecifica() {
        return pqrsfEspecifica;
    }

    public void setPqrsfEspecifica(IncidenciaEspecifica pqrsfEspecifica) {
        this.pqrsfEspecifica = pqrsfEspecifica;
    }

    public IncidenciaGeneral getPqrsfGeneral() {
        return pqrsfGeneral;
    }

    public void setPqrsfGeneral(IncidenciaGeneral pqrsfGeneral) {
        this.pqrsfGeneral = pqrsfGeneral;
    }

    public Usuario getUsuario() {
        return usuario;
    }

    public void setUsuario(Usuario usuario) {
        this.usuario = usuario;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof TrazabilidadPQRSF)) {
            return false;
        }
        TrazabilidadPQRSF other = (TrazabilidadPQRSF) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.domain.model.pqrsd.TrazabilidadPQRSF[ id=" + id + " ]";
    }
    
}
