/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.pqrsd;

import com.base16.gedsys.domain.model.archive.SeccionSubSeccion;
import com.base16.gedsys.domain.model.entityListeners.IncidenciaEspecificaListener;
import com.base16.gedsys.domain.model.user.Usuario;
import org.codehaus.jackson.annotate.JsonIgnore;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "IncidenciaEspecifica", catalog = "", schema = "")
@EntityListeners(IncidenciaEspecificaListener.class)
@XmlRootElement
@NamedQueries({
    @NamedQuery(name = "IncidenciaEspecifica.findAll", query = "SELECT i FROM IncidenciaEspecifica i")
    , @NamedQuery(name = "IncidenciaEspecifica.findById", query = "SELECT i FROM IncidenciaEspecifica i WHERE i.id = :id")
    , @NamedQuery(name = "IncidenciaEspecifica.findByDescripcion", query = "SELECT i FROM IncidenciaEspecifica i WHERE i.descripcion = :descripcion")
    , @NamedQuery(name = "IncidenciaEspecifica.findByFechaCreacion", query = "SELECT i FROM IncidenciaEspecifica i WHERE i.fechaCreacion = :fechaCreacion")
    , @NamedQuery(name = "IncidenciaEspecifica.findIncidenciasEspecificasPorLaGeneral", query = "SELECT i FROM IncidenciaEspecifica i WHERE i.incidenciaGeneral = :incidencia")
    , @NamedQuery(name="IncidenciaEspecifica.findByInvolved", query = "SELECT i FROM IncidenciaEspecifica i WHERE i.implicado = :involved")
    , @NamedQuery(name="IncidenciaEspecifica.buscarPorResponsable", query = "SELECT i FROM IncidenciaEspecifica i WHERE i.responsable = :responsable")
})
public class IncidenciaEspecifica implements Serializable {

    @JoinColumn(name = "Implicado", referencedColumnName = "Id")
    @ManyToOne(cascade = CascadeType.MERGE)
    private Usuario implicado;

    @OneToMany(mappedBy = "pqrsfEspecifica", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
    private List<TrazabilidadPQRSF> listaTrazabilidad;

    @OneToMany(mappedBy = "incidenciaEspecifica", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
    private List<ComentarioPQRSF> listaComentariosPQRSF;

    @NotNull
    @Column(name = "Estado")
    private Integer estado;

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Integer id;

    @Lob
    @Size(max = 2147483647)
    @Column(name = "Descripcion")
    private String descripcion;

    @Column(name = "FechaCreacion")
    private Date fechaCreacion;

    @Column(name = "fechaModificacion")
    private Date fechaModificacion;
    
    @Column(name = "FechaRespuestaImplicado")
    private Date fechaRespuestaImplicado;

    @Lob
    @Size(max = 2147483647)
    @Column(name = "descargos")
    private String descargos;

    @NotNull
    @JoinColumn(name = "incidenciaGeneral", referencedColumnName = "Id")
    @ManyToOne
    private IncidenciaGeneral incidenciaGeneral;

    @NotNull
    @JoinColumn(name = "Seccion", referencedColumnName = "Id")
    @ManyToOne
    private SeccionSubSeccion seccion;

    @JoinColumn(name = "Responsable", referencedColumnName = "Id")
    @ManyToOne(cascade = CascadeType.MERGE)
    private Usuario responsable;

    @JoinColumn(name = "creadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario creadoPor;

    @JoinColumn(name = "modificadoPor", referencedColumnName = "Id")
    @ManyToOne
    private Usuario modificadoPor;

    @ManyToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    @JoinTable(
            name = "IncidenciasCompartidas",
            joinColumns = @JoinColumn(name = "incidenciaEspecifica_id", nullable = false),
            inverseJoinColumns = @JoinColumn(name = "usuario_id", nullable = false)
    )
    private List<Usuario> compartidos;

    public IncidenciaEspecifica() {
    }

    public IncidenciaEspecifica(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescripcion() {
        return descripcion;
    }

    public void setDescripcion(String descripcion) {
        this.descripcion = descripcion;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    /*public String getMotivoSucedido() {
        return motivoSucedido;
    }

    public void setMotivoSucedido(String motivoSucedido) {
        this.motivoSucedido = motivoSucedido;
    }

    public String getCompromiso() {
        return compromiso;
    }

    public void setCompromiso(String compromiso) {
        this.compromiso = compromiso;
    }

    public String getObservacion() {
        return observacion;
    }

    public void setObservacion(String observacion) {
        this.observacion = observacion;
    }

    public String getFirmadoPor() {
        return firmadoPor;
    }

    public void setFirmadoPor(String firmadoPor) {
        this.firmadoPor = firmadoPor;
    }*/

    public IncidenciaGeneral getIncidenciaGeneral() {
        return incidenciaGeneral;
    }

    public void setIncidenciaGeneral(IncidenciaGeneral idIncidencia) {
        this.incidenciaGeneral = idIncidencia;
    }

    public SeccionSubSeccion getSeccion() {
        return seccion;
    }

    public void setSeccion(SeccionSubSeccion seccion) {
        this.seccion = seccion;
    }

    public Usuario getResponsable() {
        return responsable;
    }

    public void setResponsable(Usuario responsable) {
        this.responsable = responsable;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof IncidenciaEspecifica)) {
            return false;
        }
        IncidenciaEspecifica other = (IncidenciaEspecifica) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.domain.model.pqrsd.IncidenciaEspecifica[ id=" + id + " ]";
    }

    /*public Integer getEstado() {
        return estado;
    }

    public void setEstado(Integer estado) {
        this.estado = estado;
    }*/

    @XmlTransient
    @JsonIgnore
    public List<TrazabilidadPQRSF> getListaTrazabilidad() {
        return listaTrazabilidad;
    }

    public void setListaTrazabilidad(List<TrazabilidadPQRSF> trazabilidadPQRSFList) {
        this.listaTrazabilidad = trazabilidadPQRSFList;
    }

    @XmlTransient
    @JsonIgnore
    public List<ComentarioPQRSF> getListaComentariosPQRSF() {
        return listaComentariosPQRSF;
    }

    public void setListaComentariosPQRSF(List<ComentarioPQRSF> listaComentariosPQRSF) {
        this.listaComentariosPQRSF = listaComentariosPQRSF;
    }

    public Date getFechaRespuestaImplicado() {
        return fechaRespuestaImplicado;
    }

    public void setFechaRespuestaImplicado(Date fechaRespuestaImplicado) {
        this.fechaRespuestaImplicado = fechaRespuestaImplicado;
    }

    public Usuario getImplicado() {
        return implicado;
    }

    public void setImplicado(Usuario implicado) {
        this.implicado = implicado;
    }


    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    public String getDescargos() {
        return descargos;
    }

    public void setDescargos(String descargos) {
        this.descargos = descargos;
    }

    public Integer getEstado() {
        return estado;
    }

    public void setEstado(Integer estado) {
        this.estado = estado;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    public Usuario getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Usuario modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    public List<Usuario> getCompartidos() {
        return compartidos;
    }

    public void setCompartidos(List<Usuario> compartidos) {
        this.compartidos = compartidos;
    }
}
