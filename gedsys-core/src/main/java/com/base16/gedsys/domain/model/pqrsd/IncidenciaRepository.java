/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.pqrsd;

import com.base16.gedsys.domain.model.archive.SeccionSubSeccion;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR> <PERSON>
 */
public interface IncidenciaRepository {
    
    public void create(IncidenciaGeneral incidenciaGeneral);
    public void edit(IncidenciaGeneral incidenciaGeneral);
    public List<IncidenciaGeneral> listar();
    void createEspecifica(List<IncidenciaEspecifica> ltsEspecifica);
    public List<IncidenciaGeneral> finByRadicado(String radicado);
    IncidenciaEspecifica findIncidenciaEspecifica(Integer id);
    IncidenciaEspecifica findEspecificaByGerneral(IncidenciaGeneral incidenciaGeneral);
    void modificarEspecifica(IncidenciaEspecifica incidencia);
    List<IncidenciaEspecifica> findIncidenciasEspecificasPorLaGeneral(IncidenciaGeneral incidencia);
    void editGeneral(IncidenciaGeneral incidenciaGeneral);
    IncidenciaGeneral findIncidenciaGeneral(Integer id);
    List<IncidenciaGeneral> findFields(String Radicado,String NombreEntidad, String NumeroDocumentoEntidad, Date FechaDocumentoEntidad1,Date FechaDocumentoEntidad2, String Estado,String Canal, SeccionSubSeccion seccion);
    
}
