/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.domain.model.pqrsd;

import com.base16.gedsys.domain.model.archive.TipoDocumental;
import com.base16.gedsys.domain.model.attachments.Attachment;
import com.base16.gedsys.domain.model.document.Documento;
import com.base16.gedsys.domain.model.document.Entidad;
import com.base16.gedsys.domain.model.documentProducer.Carta;
import com.base16.gedsys.domain.model.documentProducer.Resolucion;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import javax.persistence.*;
import javax.validation.constraints.Size;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlTransient;

import com.base16.gedsys.domain.model.entityListeners.IncidenciaGeneralListener;
import com.base16.gedsys.domain.model.user.Usuario;
import org.codehaus.jackson.annotate.JsonIgnore;
import org.eclipse.persistence.annotations.AdditionalCriteria;

import static javax.persistence.CascadeType.MERGE;
import static javax.persistence.CascadeType.PERSIST;

/**
 * <AUTHOR> Alejandro
 */
@Entity
@Table(name = "IncidenciaGeneral", schema = "dbo")
@XmlRootElement
@EntityListeners(IncidenciaGeneralListener.class)
@AdditionalCriteria("this.deleted=false")
@NamedQueries({
        @NamedQuery(name = "IncidenciaGeneral.findAll", query = "SELECT i FROM IncidenciaGeneral i")
        , @NamedQuery(name = "IncidenciaGeneral.findById", query = "SELECT i FROM IncidenciaGeneral i WHERE i.id = :id")
        , @NamedQuery(name = "IncidenciaGeneral.findByFechaCreacion", query = "SELECT i FROM IncidenciaGeneral i WHERE i.fechaCreacion = :fechaCreacion")
        , @NamedQuery(name = "IncidenciaGeneral.findByFechaModificacion", query = "SELECT i FROM IncidenciaGeneral i WHERE i.fechaModificacion = :fechaModificacion")
        , @NamedQuery(name = "IncidenciaGeneral.findByRadicado", query = "SELECT i FROM IncidenciaGeneral i WHERE i.radicado=:radicado")})
public class IncidenciaGeneral implements Serializable {
    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Integer id;

    @Lob
    @Column(name = "asunto")
    private String asunto;

    @OneToOne(mappedBy = "incidenciaGeneral", fetch = FetchType.LAZY)
    private Carta borrador;

    @Column(name = "canalEntrada")
    private Integer canalEntrada;

    @ManyToOne
    @JoinColumn(name = "creadoPor", referencedColumnName = "id")
    private Usuario creadoPor;

    @ManyToOne
    @JoinColumn(name = "modificadoPor", referencedColumnName = "id")
    private Usuario modificadoPor;

    @Column(name = "deleted")
    private Boolean deleted;

    @Lob
    @Column(name = "descripcion")
    private String descripcion;

    @Column(name = "dueDate")
    private Date dueDate;


    @Column(name = "estado")
    private Integer estado;
    @Column(name = "fechaCreacion")
    private Date fechaCreacion;
    @Column(name = "fechaModificacion")
    private Date fechaModificacion;
    @Column(name = "fechaRespuesta")
    private Date fechaRespuesta;

    @Size(max = 50)
    @Column(name = "Radicado")
    private String radicado;

    @Size(max = 45)
    @Column(name = "medio")
    private String medio;//Preferencia en el envío de la respuesta

    @OneToOne(mappedBy = "incidenciaRespondida", fetch = FetchType.LAZY)
    private Documento respuesta;

    @XmlTransient
    @JsonIgnore
    @JoinColumn(name = "tipoIncidencia", referencedColumnName = "id")
    @ManyToOne
    private TipoDocumental tipoIncidencia;

    @OneToMany(mappedBy = "pqrsfGeneral", cascade = PERSIST, fetch = FetchType.LAZY)
    private List<TrazabilidadPQRSF> trazabilidadPQRSFList;

    @ManyToMany(cascade = {PERSIST, MERGE},
            fetch = FetchType.LAZY)
    @JoinTable(name = "AnexosIncidenciaGeneral",
            joinColumns = @JoinColumn(name = "incidenciaGeneral_id", nullable = false),
            inverseJoinColumns = @JoinColumn(name = "attachment_id", nullable = false)
    )
    private List<Attachment> attachments;

    @JoinColumn(name = "entidad", referencedColumnName = "id")
    @ManyToOne(cascade = {PERSIST, MERGE}, fetch = FetchType.LAZY)
    private Entidad entidad;

    @OneToMany(mappedBy = "incidenciaGeneral", cascade = CascadeType.ALL)
    private List<IncidenciaEspecifica> incidenciasEspecificas;

    public IncidenciaGeneral() {
    }

    public IncidenciaGeneral(Integer id) {
        this.id = id;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDescripcion() {
        return descripcion;
    }

    public void setDescripcion(String descripcion) {
        this.descripcion = descripcion;
    }

    public Date getFechaCreacion() {
        return fechaCreacion;
    }

    public void setFechaCreacion(Date fechaCreacion) {
        this.fechaCreacion = fechaCreacion;
    }

    public Date getFechaModificacion() {
        return fechaModificacion;
    }

    public void setFechaModificacion(Date fechaModificacion) {
        this.fechaModificacion = fechaModificacion;
    }

    @XmlTransient
    @JsonIgnore
    public List<IncidenciaEspecifica> getIncidenciasEspecificas() {
        return incidenciasEspecificas;
    }

    public void setIncidenciasEspecificas(List<IncidenciaEspecifica> incidenciaEspecificaList) {
        this.incidenciasEspecificas = incidenciaEspecificaList;
    }

    public Entidad getEntidad() {
        return entidad;
    }

    public void setEntidad(Entidad entidad) {
        this.entidad = entidad;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        // TODO: Warning - this method won't work in the case the id fields are not set
        if (!(object instanceof IncidenciaGeneral)) {
            return false;
        }
        IncidenciaGeneral other = (IncidenciaGeneral) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "com.base16.gedsys.domain.model.pqrsd.IncidenciaGeneral[ id=" + id + " ]";
    }

    public Integer getEstado() {
        return estado;
    }

    public void setEstado(Integer estado) {
        this.estado = estado;
    }

    public String getRadicado() {
        return radicado;
    }

    public void setRadicado(String radicado) {
        this.radicado = radicado;
    }

    public Integer getCanalEntrada() {
        return canalEntrada;
    }

    public void setCanalEntrada(Integer canalEntrada) {
        this.canalEntrada = canalEntrada;
    }

    @XmlTransient
    @JsonIgnore
    public List<TrazabilidadPQRSF> getTrazabilidadPQRSFList() {
        return Optional.ofNullable(trazabilidadPQRSFList).isPresent()
                ? trazabilidadPQRSFList : new ArrayList<>();
    }

    public void setTrazabilidadPQRSFList(List<TrazabilidadPQRSF> trazabilidadPQRSFList) {
        this.trazabilidadPQRSFList = trazabilidadPQRSFList;
    }

    public String getAsunto() {
        return asunto;
    }

    public void setAsunto(String asunto) {
        this.asunto = asunto;
    }

    public Date getFechaRespuesta() {
        return fechaRespuesta;
    }

    public void setFechaRespuesta(Date fechaRespuesta) {
        this.fechaRespuesta = fechaRespuesta;
    }

    public Date getDueDate() {
        return dueDate;
    }

    public void setDueDate(Date dueDate) {
        this.dueDate = dueDate;
    }

    public String getMedio() {
        return medio;
    }

    public void setMedio(String medio) {
        this.medio = medio;
    }

    public Documento getRespuesta() {
        return respuesta;
    }

    public void setRespuesta(Documento respuesta) {
        this.respuesta = respuesta;
    }

    public TipoDocumental getTipoIncidencia() {
        return tipoIncidencia;
    }

    public void setTipoIncidencia(TipoDocumental tipoIncidencia) {
        this.tipoIncidencia = tipoIncidencia;
    }

    public List<Attachment> getAttachments() {
        return Optional.ofNullable(attachments).orElse(new ArrayList<>());
    }

    public void setAttachments(List<Attachment> anexos) {
        this.attachments = anexos;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public Usuario getCreadoPor() {
        return creadoPor;
    }

    public void setCreadoPor(Usuario creadoPor) {
        this.creadoPor = creadoPor;
    }

    public Usuario getModificadoPor() {
        return modificadoPor;
    }

    public void setModificadoPor(Usuario modificadoPor) {
        this.modificadoPor = modificadoPor;
    }

    public Carta getBorrador() {
        return borrador;
    }

    public void setBorrador(Carta borrador) {
        this.borrador = borrador;
    }
}
