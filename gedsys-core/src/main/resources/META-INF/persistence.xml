<?xml version="1.0" encoding="UTF-8"?>
<persistence version="2.1" xmlns="http://xmlns.jcp.org/xml/ns/persistence" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/persistence http://xmlns.jcp.org/xml/ns/persistence/persistence_2_1.xsd">
  <persistence-unit name="GEDSYSPU" transaction-type="JTA">
    <provider>org.eclipse.persistence.jpa.PersistenceProvider</provider>
    <jta-data-source>jdbc/GEDSYSDS</jta-data-source>
    <class>com.base16.gedsys.domain.model.acl.Acl</class>
    <class>com.base16.gedsys.domain.model.location.Corregimiento</class>
    <class>com.base16.gedsys.domain.model.location.Departamento</class>
    <class>com.base16.gedsys.domain.model.location.Municipio</class>
    <class>com.base16.gedsys.domain.model.location.Pais</class>
    <class>com.base16.gedsys.domain.model.user.Usuariosignaturas</class>
    <class>com.base16.gedsys.domain.model.user.Usuario</class>
    <class>com.base16.gedsys.domain.model.user.GrupoUsuario</class>
    <class>com.base16.gedsys.domain.model.user.Grupo</class>
    <class>com.base16.gedsys.domain.model.location.Vereda</class>
    <class>com.base16.gedsys.domain.model.user.Cargo</class>
    <class>com.base16.gedsys.domain.model.user.Preferencias</class>
    <class>com.base16.gedsys.domain.model.archive.SubSeriesTiposDocumentales</class>
    <class>com.base16.gedsys.domain.model.archive.UnidadDocumental</class>
    <class>com.base16.gedsys.domain.model.archive.TipoDocumental</class>
    <class>com.base16.gedsys.domain.model.archive.SeccionSubSeccion</class>
    <class>com.base16.gedsys.domain.model.archive.Serie</class>
    <class>com.base16.gedsys.domain.model.archive.SubSerie</class>
    <class>com.base16.gedsys.domain.model.archive.Subseccionsubserie</class>
    <class>com.base16.gedsys.domain.model.archive.SignaturaTopografica</class>
    <class>com.base16.gedsys.domain.model.templateDocument.CamposPlantilla</class>
    <class>com.base16.gedsys.domain.model.templateDocument.Campos</class>
    <class>com.base16.gedsys.domain.model.sendTemplate.PlanillaEnvio</class>
    <class>com.base16.gedsys.domain.model.sendTemplate.PlanillaEnvioDocumento</class>
    <class>com.base16.gedsys.domain.model.notification.DiaFestivo</class>
    <class>com.base16.gedsys.domain.model.notification.Notificacion</class>
    <class>com.base16.gedsys.domain.model.documentProducer.Carta</class>
    <class>com.base16.gedsys.domain.model.documentProducer.Circular</class>
    <class>com.base16.gedsys.domain.model.documentProducer.ActaAusente</class>
    <class>com.base16.gedsys.domain.model.documentProducer.Acta</class>
    <class>com.base16.gedsys.domain.model.documentProducer.Informe</class>
    <class>com.base16.gedsys.domain.model.documentProducer.Comunicacion</class>
    <class>com.base16.gedsys.domain.model.documentProducer.ActaAsistente</class>
    <class>com.base16.gedsys.domain.model.documentProducer.CartaConCopia</class>
    <class>com.base16.gedsys.domain.model.documentProducer.Constancia</class>
    <class>com.base16.gedsys.domain.model.documentProducer.ComunicacionConCopia</class>
    <class>com.base16.gedsys.domain.model.documentProducer.Certificado</class>
    <class>com.base16.gedsys.domain.model.documentProducer.ActaInvitado</class>
    <class>com.base16.gedsys.domain.model.documentProducer.InformeConCopia</class>
    <class>com.base16.gedsys.domain.model.documentProducer.CircularConCopia</class>
    <class>com.base16.gedsys.domain.model.documentProducer.Formatos</class>
    <class>com.base16.gedsys.domain.model.document.ConsecutivosUsuario</class>
    <class>com.base16.gedsys.domain.model.document.ClaseDocumento</class>
    <class>com.base16.gedsys.domain.model.document.Sede</class>
    <class>com.base16.gedsys.domain.model.document.Comentario</class>
    <class>com.base16.gedsys.domain.model.document.Trazabilidad</class>
    <class>com.base16.gedsys.domain.model.document.Consecutivo</class>
    <class>com.base16.gedsys.domain.model.document.DestinatariosDocumento</class>
    <class>com.base16.gedsys.domain.model.document.Entidad</class>
    <class>com.base16.gedsys.domain.model.document.MonitoresProceso</class>
    <class>com.base16.gedsys.domain.model.document.MensajeriaDocumental</class>
    <class>com.base16.gedsys.domain.model.document.Documento</class>
    <class>com.base16.gedsys.domain.model.document.Mediorecepcion</class>
    <class>com.base16.gedsys.domain.model.document.ProcesoTipoDocumento</class>
    <class>com.base16.gedsys.domain.model.document.ProcesoNegocio</class>
    <class>com.base16.gedsys.domain.model.document.Autor</class>
    <class>com.base16.gedsys.domain.model.document.TipoDocumento</class>
    <class>com.base16.gedsys.domain.model.document.ProcesoDocumental</class>
    <class>com.base16.gedsys.domain.model.document.Transportador</class>
    <class>com.base16.gedsys.domain.model.document.Prestamo</class>
    <class>com.base16.gedsys.application.Configuracion</class>
    <class>com.base16.gedsys.application.Modulo</class>
    <class>com.base16.gedsys.application.Log</class>
    <class>com.base16.gedsys.domain.model.user.UsuarioSeccion</class>
    <class>com.base16.gedsys.domain.model.pqrsd.IncidenciaEspecifica</class>
    <class>com.base16.gedsys.domain.model.pqrsd.IncidenciaGeneral</class>
    <class>com.base16.gedsys.domain.model.pqrsd.TrazabilidadPQRSF</class>
    <class>com.base16.gedsys.domain.model.document.InventarioDocumental</class>
    <class>com.base16.gedsys.domain.model.document.PlanillaDistribucionDocumental</class>
    <class>com.base16.gedsys.domain.model.document.PlanillaDistribucionXMensajeriaDocumental</class>
    <class>com.base16.gedsys.domain.model.user.LoginAudit</class>
    <class>com.base16.gedsys.domain.model.document.ComentarioBusquedaRadicado</class>
    <class>com.base16.gedsys.domain.model.list.Listas</class>
    <class>com.base16.gedsys.domain.model.document.TransferenciaDocumental</class>
    <!--<shared-cache-mode>ALL</shared-cache-mode>-->
    <class>com.base16.gedsys.domain.model.documentProducer.InformeRemInterno</class>
    <class>com.base16.gedsys.domain.model.documentProducer.ComunicacionRemInterno</class>
    <class>com.base16.gedsys.domain.model.documentProducer.CircularRemInterno</class>
    <class>com.base16.gedsys.domain.model.documentProducer.Resolucion</class>
    <class>com.base16.gedsys.domain.model.documentProducer.TrazabilidadProdDoc</class>
    <class>com.base16.gedsys.domain.model.document.Anexos</class>
    <class>com.base16.gedsys.domain.model.archive.TrazabilidadUnidadDoc</class>
    <class>com.base16.gedsys.domain.model.attachments.Attachment</class>
    <class>com.base16.gedsys.domain.model.attachments.TraceabilityAttachment</class>

    <shared-cache-mode>NONE</shared-cache-mode>
    <properties>
      <property name="eclipselink.cache.shared.default" value="false" />
    </properties>
    <!--    <properties>-->
      <!--
      <property name="javax.persistence.jdbc.url" value="***************************************************"/>
      <property name="javax.persistence.jdbc.user" value="sa"/>
      <property name="javax.persistence.jdbc.driver" value="com.microsoft.sqlserver.jdbc.SQLServerDriver"/>
      <property name="javax.persistence.jdbc.password" value="b4s316.c0"/>
      -->
<!--      <property name="javax.persistence.jdbc.url" value="*****************************"/>-->
<!--      <property name="javax.persistence.jdbc.user" value="root"/>-->
<!--      <property name="javax.persistence.jdbc.driver" value="com.mysql.cj.jdbc.Driver"/>-->
<!--      <property name="javax.persistence.jdbc.password" value="root"/>-->
      <!--<property name="eclipselink.logging.level" value="FINE"/>-->
      <!--<property name="eclipselink.profiler" value="PerformanceMonitor"/>-->
      <!--<property name="eclipselink.profiler" value="QueryMonitor"/>-->
<!--    </properties>-->
  </persistence-unit>
</persistence>
