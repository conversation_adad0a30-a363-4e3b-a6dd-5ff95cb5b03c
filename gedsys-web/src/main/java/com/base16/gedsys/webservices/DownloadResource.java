/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.base16.gedsys.webservices;

import com.base16.gedsys.bean.ArchivarEstructuraDirectorio;
import com.base16.gedsys.bean.BaseBean;
import com.base16.gedsys.bean.TrazabilidadBean;
import com.base16.gedsys.configuracion.ConfiguracionServicesImpl;
import com.base16.gedsys.domain.model.document.Documento;
import com.base16.gedsys.domain.model.pqrsd.IncidenciaGeneral;
import com.base16.gedsys.domain.model.user.Usuario;
import com.base16.gedsys.infrastructure.persistence.jpa.DocumentoJpaRepository;
import com.base16.gedsys.infrastructure.persistence.jpa.IncidenciaJpaRepository;
import com.base16.gedsys.infrastructure.persistence.jpa.UsuarioJpaRepository;
import com.base16.utils.CryptoException;
import com.base16.utils.CryptoUtils;
import com.base16.utils.KeyConfiguration;
import com.base16.utils.DownloadTokenUtil;
import io.jsonwebtoken.JwtException;
import com.sys.gedsys.pqrsf.TrazabilidadPqrsf;
import com.sys.gedsys.pqrsf.impl.TrazabilidadPqrsfImpl;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;

import javax.enterprise.context.RequestScoped;
import javax.inject.Inject;
import javax.transaction.SystemException;
import javax.ws.rs.*;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.Optional;
import java.util.UUID;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * REST Web Service
 *
 * <AUTHOR>
 */
@Path("Download")
@RequestScoped
public class DownloadResource extends BaseBean {

  @Inject
  private TrazabilidadBean trazabilidadBean;
  @Inject
  private DocumentoJpaRepository documentoJpaRepository;
  @Inject
  private IncidenciaJpaRepository incidenciaJpaRepository;
  @Inject
  private UsuarioJpaRepository usuarioJpaRepository;
  @Inject
  private com.base16.gedsys.infrastructure.persistence.jpa.AnexosJpaController anexosJpaController;


  /**
   * Creates a new instance of DownloadResource
   */
  public DownloadResource() {
  }

  @GET
  @Path("/pdf")
  @Produces({"application/pdf"})
  public byte[] downloadPdfFile(@QueryParam("id") Long id,
      @QueryParam("currentUser") Integer idUsuario,
      @QueryParam("token") String token,
      @HeaderParam("authorization") String authStringEnc) {
    byte[] data;
    File filePath;

    // Validate download token if provided (required for PQRSF public downloads)
    if (token != null && !token.isEmpty()) {
      try {
        // Note: radicado validation is currently null; enhance by adding radicado query param
        // or resolving it server-side from documento relations for full security
        DownloadTokenUtil.validateToken(token, null, id.toString(), "documento");
        Logger.getLogger(this.getClass().getName()).log(Level.INFO,
            "Token validated for document ID: " + id + " (radicado not verified - consider adding radicado param)");
      } catch (JwtException e) {
        Logger.getLogger(this.getClass().getName()).log(Level.WARNING,
            "Invalid download token for document ID: " + id, e);
        // Return "access denied" placeholder
        filePath = new File((ConfiguracionServicesImpl.consultarValorConfigGeneral(
            KeyConfiguration.keyPathData.getKeyConfiguration())) + File.separatorChar
            + "noexiste.pdf");
        try (final InputStream stream = new FileInputStream(filePath)) {
          return IOUtils.toByteArray(stream);
        } catch (Exception ex) {
          throw new RuntimeException(ex);
        }
      }
    }

    try {
      Documento documento = documentoJpaRepository.find(id);
      filePath = new File(SourceFolder(documento, documento.getNombreDocumento()).toURI());
      trazabilidadDocumento(idUsuario, documento);
    } catch (RuntimeException e) {
      filePath = new File((ConfiguracionServicesImpl.consultarValorConfigGeneral(
          KeyConfiguration.keyPathData.getKeyConfiguration())) + File.separatorChar
          + "noexiste.pdf");
    }

    try (final InputStream stream = new FileInputStream(filePath)) {
      data = IOUtils.toByteArray(stream);
    } catch (Exception e) {
      Logger.getLogger(this.getClass().getName()).log(Level.SEVERE, e.getMessage());
      throw new RuntimeException(e);
    }

    return data;
  }

  @GET
  @Path("/pdfDocs")
  @Produces({"application/pdf"})
  public byte[] downloadPdfFileArchivar(@QueryParam("id") Long id,
      @QueryParam("currentUser") Integer idUsuario,
      @QueryParam("tipoDocumento") String tipoDocumento,
      @QueryParam("token") String token,
      @HeaderParam("authorization") String authStringEnc) {
    byte[] data = null;

    // Validate download token if provided (required for PQRSF public downloads)
    if (token != null && !token.isEmpty()) {
      try {
        // Note: radicado validation is currently null; enhance by adding radicado query param
        // or resolving it server-side from documento relations for full security
        DownloadTokenUtil.validateToken(token, null, id.toString(), "anexo-legacy");
        Logger.getLogger(this.getClass().getName()).log(Level.INFO,
            "Token validated for pdfDocs ID: " + id + " (radicado not verified - consider adding radicado param)");
      } catch (JwtException e) {
        Logger.getLogger(this.getClass().getName()).log(Level.WARNING,
            "Invalid download token for pdfDocs ID: " + id, e);
        // Return "access denied" placeholder
        File notFoundFile = new File((ConfiguracionServicesImpl.consultarValorConfigGeneral(
            KeyConfiguration.keyPathData.getKeyConfiguration())) + File.separatorChar
            + "noexiste.pdf");
        try (FileInputStream fis = new FileInputStream(notFoundFile)) {
          return IOUtils.toByteArray(fis);
        } catch (Exception ex) {
          Logger.getLogger(DownloadResource.class.getName()).log(Level.SEVERE, null, ex);
          return null;
        }
      }
    }

    try {
      //TODO> pendiente decifrar archivo y tomar la ubicacion del mismo.
      Documento documento = documentoJpaRepository.find(id);
      File tempFile = null;
      switch (tipoDocumento) {
        case "comprobante":
          tempFile = SourceFolder(documento, documento.getComprobante());
          break;
        case "guia":
          tempFile = SourceFolder(documento, documento.getGuia());
          break;
        case "anexos":
          // Apply two-stage fallback resolution for anexos (consistent with downloadAnexo method)
          String anexoDoc = documento.getAnexoDocumento();
          if (anexoDoc != null && !anexoDoc.isEmpty()) {
            // First try the direct path via SourceFolder
            tempFile = SourceFolder(documento, anexoDoc);

            // If file doesn't exist, apply two-stage fallback using RutaAnexo
            if ((tempFile == null || !tempFile.exists()) && documento.getRutaAnexo() != null) {
              String pathData = ConfiguracionServicesImpl.consultarValorConfigGeneral(
                  KeyConfiguration.keyPathData.getKeyConfiguration());

              // Stage 1: Try without extension
              tempFile = new File(pathData + File.separatorChar + documento.getRutaAnexo());

              // Stage 2: If file doesn't exist and extension is available, try with extension
              if (!tempFile.exists()) {
                String extension = documento.getExtension();
                if (extension != null && !extension.isEmpty()) {
                  String rutaConExtension = documento.getRutaAnexo() + "." + extension;
                  tempFile = new File(pathData + File.separatorChar + rutaConExtension);
                  Logger.getLogger(this.getClass().getName()).log(Level.INFO,
                      "File not found without extension, trying with extension: " + tempFile.getAbsolutePath());
                } else {
                  // Fallback: Try to detect file with extension by globbing if extension is unknown
                  File baseFile = new File(pathData + File.separatorChar + documento.getRutaAnexo());
                  if (baseFile.getParentFile() != null && baseFile.getParentFile().exists()) {
                    String[] matchingFiles = baseFile.getParentFile().list((dir, name) ->
                        name.startsWith(baseFile.getName() + "."));
                    if (matchingFiles != null && matchingFiles.length > 0) {
                      tempFile = new File(baseFile.getParent() + File.separator + matchingFiles[0]);
                      Logger.getLogger(this.getClass().getName()).log(Level.INFO,
                          "Resolved anexo file with globbing: " + tempFile.getAbsolutePath());
                    }
                  }
                }
              }
            }
          }
          break;
        case "anexoPqrsf":
          tempFile = new File((ConfiguracionServicesImpl.consultarValorConfigGeneral(
              KeyConfiguration.keyPathData.getKeyConfiguration())) + File.separatorChar + "PQRSF"
              + File.separator + documento.getRutaArchivo());
          break;
        case "documento":
          tempFile = SourceFolder(documento, documento.getNombreDocumento());
          break;
      }
      if ((tempFile == null)
          || (!tempFile.exists())) {
        tempFile = new File((ConfiguracionServicesImpl.consultarValorConfigGeneral(
            KeyConfiguration.keyPathData.getKeyConfiguration())) + File.separatorChar
            + "noexiste.pdf");
        FileInputStream fis = new FileInputStream(tempFile);
        data = IOUtils.toByteArray(fis);
      }
      if (!Optional.ofNullable(data).isPresent()) {
        if (tempFile.exists()) {
          if (Boolean.parseBoolean(ConfiguracionServicesImpl.consultarValorConfigGeneral(
              KeyConfiguration.keyProtectFile.getKeyConfiguration())) == true) {
            UUID guid = UUID.randomUUID();
            File outputFile = new File((ConfiguracionServicesImpl.consultarValorConfigGeneral(
                KeyConfiguration.keyPathData.getKeyConfiguration())) + File.separatorChar
                + guid.toString());
            CryptoUtils.decrypt("Mary has one cat", tempFile, outputFile);
            FileInputStream fis = new FileInputStream(outputFile);
            data = IOUtils.toByteArray(fis);
            trazabilidadDocumento(idUsuario, documento);
          } else {
            FileInputStream fis = new FileInputStream(tempFile);
            data = IOUtils.toByteArray(fis);
            trazabilidadDocumento(idUsuario, documento);
          }
        }
      }

    } catch (IOException | CryptoException ex) {
      Logger.getLogger(DownloadResource.class.getName()).log(Level.SEVERE, null, ex);
    }
    return data;
  }

  @GET
  @Path("/pdfbase")
  public String descargarPdfBase(@QueryParam("id") Long id
  ) {
    String archivo64 = "";
    try {
      //TODO> pendiente decifrar archivo y tomar la ubicacion del mismo.
      Documento documento = documentoJpaRepository.find(id);
      if (documento.getNombreDocumento() != null) {
        File tempFile = SourceFolder(documento, documento.getNombreDocumento());
        if (tempFile.exists()) {
          byte[] filecontent = FileUtils.readFileToByteArray(tempFile);
          archivo64 = Base64.getEncoder().encodeToString(filecontent);
        }
      }

    } catch (IOException ex) {
      Logger.getLogger(DownloadResource.class.getName()).log(Level.SEVERE, null, ex);
    }
    return archivo64;
  }

  public File SourceFolder(Documento doc, String tipoDocumento) {
    if (doc.getNombreDocumento() == null) {
      throw new RuntimeException("File not found on disk");
    }
    ArchivarEstructuraDirectorio archivar = new ArchivarEstructuraDirectorio();
    File ruta = null;
    File rutaFinal;
    if (doc.getSeccion() != null && doc.getUnidadDocumental() != null
        && doc.getArchivado() == true) {
      File rutaArchivado = archivar.rutaGeneral("Archivado", doc.getSeccion().getNombre(),
          doc.getSerie().getNombre(), doc.getSubSerie().getNombre(),
          doc.getUnidadDocumental().getNombre());
      rutaFinal = new File(rutaArchivado.getAbsolutePath() + File.separatorChar + tipoDocumento);
      if (rutaFinal.exists()) {
        ruta = rutaFinal;
      }
    }
    if (ruta == null) {
      File rutaNoArchivado = archivar.rutaGeneral("No_Archivado", "", "", "", "");
      rutaFinal = new File(rutaNoArchivado.getAbsolutePath() + File.separatorChar + tipoDocumento);
      if (rutaFinal.exists()) {
        ruta = rutaFinal;
      }
    }
    return ruta;
  }

  private void trazabilidadDocumento(Integer idUsuario, Documento documento) {
    Usuario currentUser;
    if (Optional.ofNullable(idUsuario).isPresent()) {
      currentUser = usuarioJpaRepository.find(idUsuario);
      trazabilidadBean.register("Documento Visualizado", documento, currentUser);
    }
  }

  @GET
  @Path("/anexo")
  @Produces({"application/pdf", "application/octet-stream"})
  public byte[] downloadAnexo(@QueryParam("idAnexo") Integer idAnexo,
                              @QueryParam("token") String token) {
    byte[] data = null;

    // Validate download token (required for PQRSF public downloads)
    if (token == null || token.isEmpty()) {
      Logger.getLogger(this.getClass().getName()).log(Level.WARNING,
          "Missing download token for anexo ID: " + idAnexo);
      // Return "access denied" placeholder
      File notFoundFile = new File((ConfiguracionServicesImpl.consultarValorConfigGeneral(
          KeyConfiguration.keyPathData.getKeyConfiguration())) + File.separatorChar
          + "noexiste.pdf");
      try (FileInputStream fis = new FileInputStream(notFoundFile)) {
        return IOUtils.toByteArray(fis);
      } catch (Exception ex) {
        Logger.getLogger(DownloadResource.class.getName()).log(Level.SEVERE, null, ex);
        return null;
      }
    }

    try {
      // Note: radicado validation is currently null; enhance by adding radicado query param
      // or resolving it server-side from anexo -> documento -> incidencia relations for full security
      DownloadTokenUtil.validateToken(token, null, idAnexo.toString(), "anexo");
      Logger.getLogger(this.getClass().getName()).log(Level.INFO,
          "Token validated for anexo ID: " + idAnexo + " (radicado not verified - consider adding radicado param)");
    } catch (JwtException e) {
      Logger.getLogger(this.getClass().getName()).log(Level.WARNING,
          "Invalid download token for anexo ID: " + idAnexo, e);
      // Return "access denied" placeholder
      File notFoundFile = new File((ConfiguracionServicesImpl.consultarValorConfigGeneral(
          KeyConfiguration.keyPathData.getKeyConfiguration())) + File.separatorChar
          + "noexiste.pdf");
      try (FileInputStream fis = new FileInputStream(notFoundFile)) {
        return IOUtils.toByteArray(fis);
      } catch (Exception ex) {
        Logger.getLogger(DownloadResource.class.getName()).log(Level.SEVERE, null, ex);
        return null;
      }
    }

    try {
      com.base16.gedsys.domain.model.document.Anexos anexo = anexosJpaController.find(idAnexo);
      if (anexo == null || anexo.getRutaAnexo() == null || anexo.getRutaAnexo().isEmpty()) {
        // Return "not found" placeholder file
        File notFoundFile = new File((ConfiguracionServicesImpl.consultarValorConfigGeneral(
            KeyConfiguration.keyPathData.getKeyConfiguration())) + File.separatorChar
            + "noexiste.pdf");
        try (FileInputStream fis = new FileInputStream(notFoundFile)) {
          data = IOUtils.toByteArray(fis);
        }
        return data;
      }

      // Build the file path from rutaAnexo with two-stage fallback resolution
      String pathData = ConfiguracionServicesImpl.consultarValorConfigGeneral(
          KeyConfiguration.keyPathData.getKeyConfiguration());

      // Stage 1: Try to load file using rutaAnexo as-is (without extension)
      File anexoFile = new File(pathData + File.separatorChar + anexo.getRutaAnexo());

      // Stage 2: If file doesn't exist and formato is present, try with extension appended
      if (!anexoFile.exists() && anexo.getFormato() != null && !anexo.getFormato().isEmpty()) {
        String rutaConExtension = anexo.getRutaAnexo() + "." + anexo.getFormato();
        anexoFile = new File(pathData + File.separatorChar + rutaConExtension);
        Logger.getLogger(this.getClass().getName()).log(Level.INFO,
            "File not found without extension, trying with extension: " + anexoFile.getAbsolutePath());
      }

      if (!anexoFile.exists()) {
        // Log the missing file path for debugging
        Logger.getLogger(this.getClass().getName()).log(Level.WARNING,
            "Anexo file not found at path: " + anexoFile.getAbsolutePath());

        // Return "not found" placeholder file
        File notFoundFile = new File(pathData + File.separatorChar + "noexiste.pdf");
        try (FileInputStream fis = new FileInputStream(notFoundFile)) {
          data = IOUtils.toByteArray(fis);
        }
        return data;
      }

      // Handle encryption if enabled
      if (Boolean.parseBoolean(ConfiguracionServicesImpl.consultarValorConfigGeneral(
          KeyConfiguration.keyProtectFile.getKeyConfiguration()))) {
        UUID guid = UUID.randomUUID();
        File outputFile = new File((ConfiguracionServicesImpl.consultarValorConfigGeneral(
            KeyConfiguration.keyPathData.getKeyConfiguration())) + File.separatorChar
            + guid.toString());
        CryptoUtils.decrypt("Mary has one cat", anexoFile, outputFile);
        try (FileInputStream fis = new FileInputStream(outputFile)) {
          data = IOUtils.toByteArray(fis);
        }
      } else {
        try (FileInputStream fis = new FileInputStream(anexoFile)) {
          data = IOUtils.toByteArray(fis);
        }
      }

    } catch (IOException | CryptoException ex) {
      Logger.getLogger(DownloadResource.class.getName()).log(Level.SEVERE, null, ex);
    }
    return data;
  }

}
