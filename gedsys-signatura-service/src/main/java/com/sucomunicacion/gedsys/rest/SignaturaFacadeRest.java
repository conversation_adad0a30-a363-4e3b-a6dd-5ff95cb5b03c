package com.sucomunicacion.gedsys.rest;

import com.sucomunicacion.gedsys.model.SignaturaTopografica;
import com.sucomunicacion.gedsys.service.AbstractFacade;

import javax.enterprise.context.ApplicationScoped;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.transaction.Transactional;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import java.util.List;

@ApplicationScoped
@Path("Signatura")
public class SignaturaFacadeRest extends AbstractFacade<SignaturaTopografica> {

    @PersistenceContext(unitName = "GedsysPU")
    private EntityManager em;

    public SignaturaFacadeRest() {
        super(SignaturaTopografica.class);
    }

    @POST
    @Override
    @Consumes({MediaType.APPLICATION_JSON})
    @Transactional(Transactional.TxType.REQUIRES_NEW)
    public void create(SignaturaTopografica entity){
        super.create(entity);
    }

    @PUT
    @Path("{id}")
    @Consumes({MediaType.APPLICATION_JSON})
    @Transactional(Transactional.TxType.REQUIRES_NEW)
    public void edit(@PathParam("id") String id, SignaturaTopografica entity){
        super.edit(entity);
    }

    @DELETE
    @Path("{id}")
    @Transactional(Transactional.TxType.REQUIRES_NEW)
    public void remove(@PathParam("id") Integer id){
        super.remove(super.find(id));
    }

    @GET
    @Path("{id}")
    @Produces({MediaType.APPLICATION_JSON})
    public  SignaturaTopografica find(@PathParam("id") Integer id){
        return super.find(id);
    }


    @GET
    @Override
    @Produces({MediaType.APPLICATION_JSON})
    public List<SignaturaTopografica> findAll(){
        return super.findAll();
    }

    @Override
    protected EntityManager getEntityManager() {
        return em;
    }
}
