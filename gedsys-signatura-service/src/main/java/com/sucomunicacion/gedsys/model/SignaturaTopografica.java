
package com.sucomunicacion.gedsys.model;

import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import javax.persistence.Basic;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedQueries;
import javax.persistence.NamedQuery;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 */
@Entity
@Table(name = "signaturatopografica")
@NamedQueries({
        @NamedQuery(name = "SignaturaTopografica.findAll", query = "SELECT s FROM SignaturaTopografica s")
})
public class SignaturaTopografica implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Basic(optional = false)
    @Column(name = "Id")
    private Long id;

    @Column(name = "Borrado")
    private Boolean borrado;

    @Column(name = "Codigo")
    private String codigo;

    @Column(name = "FechaCracion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaCracion;

    @Column(name = "FechaModificacion")
    @Temporal(TemporalType.TIMESTAMP)
    private Date fechaModificacion;

    @Column(name = "Nivel")
    private Integer nivel;

    @Column(name = "Nombre")
    private String nombre;

    @Column(name = "CreadoPor")
    private int creadoPor;

    @Column(name = "DependeDe")
    private int dependeDe;

    @Column(name = "ModificadoPor")
    private int modificadoPro;

    @Size(max = 45)
    @Column(name = "Tipo")
    private String tipo;

    public SignaturaTopografica() {
    }

    public SignaturaTopografica(Boolean borrado, String codigo, Date fechaCracion, Date fechaModificacion, Integer nivel, String nombre, int creadoPor, int dependeDe, int modificadoPro, String tipo) {
        this.borrado = borrado;
        this.codigo = codigo;
        this.fechaCracion = fechaCracion;
        this.fechaModificacion = fechaModificacion;
        this.nivel = nivel;
        this.nombre = nombre;
        this.creadoPor = creadoPor;
        this.dependeDe = dependeDe;
        this.modificadoPro = modificadoPro;
        this.tipo = tipo;
    }

    @Override
    public int hashCode() {
        int hash = 0;
        hash += (id != null ? id.hashCode() : 0);
        return hash;
    }

    @Override
    public boolean equals(Object object) {
        if (!(object instanceof SignaturaTopografica)) {
            return false;
        }
        SignaturaTopografica other = (SignaturaTopografica) object;
        if ((this.id == null && other.id != null) || (this.id != null && !this.id.equals(other.id))) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return this.nombre;
    }
}
